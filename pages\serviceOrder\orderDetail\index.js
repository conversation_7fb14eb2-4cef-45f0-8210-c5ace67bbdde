import orderApi from '../../../api/modules/order.js';
import payApi from '../../../api/modules/pay';
import photoWallApi from '../../../api/modules/photoWall.js';
import addressApi from '../../../api/modules/address.js';
import utils from '../../utils/util';
import WeMessage from '../../../common/WeMessage.js';
import Session from '../../../common/Session.js';
import { OrderStatus } from '../../../common/constant.js';
import additionalServiceApi from '../../../api/modules/additionalService.js';

Page({
  data: {
    userInfo: null,
    orderDetail: {}, // 订单
    servicePhotos: null, // 服务照片
    photoWallData: null, // 照片墙数据
    additionalServices: [], // 追加服务列表
    showMoreActions: false,
    showModal: false,
    modalTitle: '敬请期待',
    modalContent: '支付功能稍后上线！',
    modalButtons: [],
    clickEvent: () => {
      // 返回上一页
      wx.navigateBack({
        delta: 1,
      });
    },

    // 追加服务支付相关
    paying: false,
    currentPayService: null, // 当前要支付的追加服务

    // 地址修改相关
    showAddressModal: false,
    addressForm: {
      address: '',
      addressDetail: '',
      addressRemark: '',
      longitude: null,
      latitude: null,
      addressId: null
    },
    userAddressList: [],
    selectedAddressType: 'manual', // 'saved' 或 'manual'
    addressModifying: false,

    // 待执行的动作
    pendingAction: null,

    // 追加服务状态映射
    additionalServiceStatusMap: {
      pending_confirm: {
        text: '待确认',
        color: '#ff9500',
        desc: '等待员工确认',
        actions: ['view', 'delete']
      },
      confirmed: {
        text: '已确认',
        color: '#007aff',
        desc: '请尽快完成支付',
        actions: ['pay', 'view', 'delete']
      },
      paid: {
        text: '已付款',
        color: '#34c759',
        desc: '服务进行中',
        actions: ['view']
      },
      rejected: {
        text: '已拒绝',
        color: '#ff3b30',
        desc: '申请被拒绝',
        actions: ['view', 'delete']
      }
    },
  },

  /**
   * 判断是否可以给员工打电话
   * 订单被接单后（待服务、已出发、服务中）到订单结束前可以打电话
   */
  canCallEmployee() {
    const { orderDetail } = this.data;
    if (!orderDetail || !orderDetail.status) {
      return false;
    }

    const callableStatuses = [
      OrderStatus.待服务,
      OrderStatus.已出发,
      OrderStatus.服务中
    ];

    return callableStatuses.includes(orderDetail.status);
  },

  /**
   * 拨打员工电话
   */
  callEmployee() {
    const { orderDetail } = this.data;

    if (!this.canCallEmployee()) {
      wx.showToast({
        title: '当前订单状态不支持联系员工',
        icon: 'none'
      });
      return;
    }

    if (!orderDetail.employee || !orderDetail.employee.phone) {
      wx.showToast({
        title: '员工联系方式不可用',
        icon: 'none'
      });
      return;
    }

    wx.makePhoneCall({
      phoneNumber: orderDetail.employee.phone,
      success: () => {
        console.log('成功发起电话呼叫');
      },
      fail: (err) => {
        console.error('电话呼叫失败', err);
        wx.showToast({
          title: '拨打电话失败',
          icon: 'none'
        });
      }
    });
  },

  onLoad(options) {
    const { data, actionType } = options;
    // 使用页面扩展的用户信息获取方式，如果没有则使用session获取
    const userInfo = this.data.userInfo || this.$session?.getUser() || Session.getUser();

    this.setData({
      userInfo,
      pendingAction: actionType // 保存待执行的动作
    });

    // 加载初始订单数据
    this.loadOrders(data);
  },
  onShow() {
    // 确保用户信息是最新的
    const userInfo = this.data.userInfo || this.$session?.getUser() || Session.getUser();
    if (userInfo && !this.data.userInfo) {
      this.setData({ userInfo });
    }

    // 如果订单状态是服务中，刷新追加服务列表
    const { orderDetail } = this.data;
    if (orderDetail && orderDetail.status === OrderStatus.服务中 && orderDetail.orderDetails && orderDetail.orderDetails.length > 0) {
      // 添加小延迟确保从申请页面返回时能获取到最新数据
      setTimeout(() => {
        this.loadAdditionalServices(orderDetail.orderDetails[0].id);
      }, 100);
    }

    // 更新是否可以打电话的状态和地址修改按钮状态
    if (orderDetail) {
      this.setData({
        canCallEmployee: this.canCallEmployee(),
        canModifyAddress: this.canModifyServiceAddress()
      });
    }
  },
  // 加载订单数据
  loadOrders(data) {
    wx.showLoading({
      title: '加载中',
    });

    const mockOrder = JSON.parse(data);
    const { serviceTime, orderTime, createdAt, ...rest } = mockOrder;
    const orderDetail = {
      ...rest,
      serviceTime: serviceTime ? utils.formatNormalDate(serviceTime) : null,
      // 下单时间
      orderTime: orderTime ? utils.formatNormalDate(orderTime) : null,
      // createdAt: createdAt ? utils.formatNormalDate(createdAt) : null,
    };

    this.setData({
      orderDetail,
      canCallEmployee: this.canCallEmployee(),
      canModifyAddress: this.canModifyServiceAddress()
    });

    // 如果订单状态是服务中、已完成或已评价，加载服务照片和照片墙数据
    if (orderDetail.status === OrderStatus.服务中 ||
        orderDetail.status === OrderStatus.已完成 ||
        orderDetail.status === OrderStatus.已评价) {
      this.loadServicePhotos(orderDetail.id);
      this.loadPhotoWallData(orderDetail.id);
    }

    // 如果订单状态是服务中，加载追加服务
    if (orderDetail.status === OrderStatus.服务中 && orderDetail.orderDetails && orderDetail.orderDetails.length > 0) {
      this.loadAdditionalServices(orderDetail.orderDetails[0].id);
    }

    wx.hideLoading();

    // 执行待执行的动作
    this.executePendingAction();
  },

  /**
   * 执行待执行的动作
   */
  executePendingAction() {
    const { pendingAction } = this.data;

    if (pendingAction === 'modifyAddress') {
      // 延迟一点时间确保页面渲染完成
      setTimeout(() => {
        this.modifyServiceAddress();
        // 清除待执行的动作
        this.setData({ pendingAction: null });
      }, 300);
    }
  },

  // 删除订单
  cancelOrder(e) {
    const orderId = e.currentTarget.dataset.orderId;
    wx.showModal({
      title: '取消订单',
      content: '确定要取消此订单吗？',
      success: res => {
        if (res.confirm) {
          const userInfo = this.data.userInfo;
          orderApi.cancel(userInfo.id, orderId).then(res => {
            if (res) {
              wx.showToast({
                title: '取消成功',
                icon: 'success',
              });
              wx.navigateBack({
                delta: 1,
              });
            }
          });
        }
      },
    });
  },
  // 切换更多操作弹窗
  toggleOrderActions(e) {
    this.setData({
      showMoreActions: !this.data.showMoreActions,
    });
  },
  // 支付订单
  payOrder(e) {
    const _this = this;
    const userInfo = this.data.userInfo;
    if (!userInfo) {
      this.navigateTo({
        type: 'tip',
        tip: '获取订单信息失败，请重新登录',
      });
      return;
    }
    const sn = e.currentTarget.dataset.sn;
    const order = this.data.orderDetail;

    if (!order) {
      wx.showToast({
        title: '订单信息不存在',
        icon: 'none',
      });
      return;
    }

    if (Number(order.amount) === 0) {
      wx.showLoading({
        title: '处理中',
      });
      // 使用统一的0元订单处理方法
      orderApi.handleZeroAmountOrder(userInfo.id, sn).then(res => {
        wx.hideLoading();
        if (res.success) {
          // 显示自定义模态框
          // _this.setData({
          //   modal: {
          //     isShow: false,
          //   },
          //   showModal: true,
          //   modalTitle: '提交成功',
          //   modalContent: '护理师会尽快和您联系，请耐心等待',
          // });
          // 刷新订单详情
          _this.loadOrders(sn);
          console.log('6----', userInfo);
          _this.handlePaymentSuccessModal(userInfo.openid, order.id);
        } else {
          wx.showToast({
            title: res.error || '处理失败，请稍后重试',
            icon: 'none',
          });
        }
      });
      return;
    }

    // 支付
    payApi.doPay({
      sn,
      onOk: () => {
        // 支付成功后，调用支付成功接口更新订单状态
        orderApi.pay(userInfo.id, sn).then(res => {
          // 显示自定义模态框
          // _this.setData({
          //   modal: {
          //     isShow: false,
          //   },
          //   showModal: true,
          //   modalTitle: '提交成功',
          //   modalContent: '护理师会尽快和您联系，请耐心等待',
          // });
          // 刷新订单详情
          _this.loadOrders(sn);
          console.log('7----', userInfo);
          _this.handlePaymentSuccessModal(userInfo.openid, order.id);
        });
      },
      onCancel: () => {
        wx.showToast({
          title: '取消支付',
          icon: 'none',
        });
      },
      onError: () => {
        wx.showToast({
          title: '支付失败',
          icon: 'none',
        });
      },
      complete: () => {},
    });
  },
  /**
   * 处理支付成功后的弹窗 - 订单详情页面
   * @param {string} openId 用户openId
   * @param {string} sn 订单编号
   */
  handlePaymentSuccessModal(openId, sn) {
    const weMessage = new WeMessage(openId, sn, OrderStatus.待接单);
    const modalConfig = weMessage.handlePaymentSuccess();
    if (modalConfig) {
      this.setData({
        showModal: true,
        modalTitle: modalConfig.modalConfig.title,
        modalContent: modalConfig.modalConfig.content,
        modalButtons: modalConfig.modalConfig.buttons,
      });
      this._weMessage = weMessage;
    }
  },
  // 处理订阅确认按钮点击
  handleModalConfirm(e) {
    if (this._weMessage) {
      this._weMessage.requestOrderConfirmationSubscribe();
    }
    this.setData({ showModal: false });
    this.data.clickEvent && this.data.clickEvent();
  },
  // 处理订阅取消按钮点击
  handleModalCancel(e) {
    if (this._weMessage) {
      this._weMessage.recordUserChoice(false);
    }
    this.setData({ showModal: false });
    this.data.clickEvent && this.data.clickEvent();
  },

  // 加载服务照片
  async loadServicePhotos(orderId) {
    try {
      const servicePhotos = await orderApi.getServicePhotos(orderId);
      if (servicePhotos) {
        // 格式化时间
        const formattedPhotos = {
          ...servicePhotos,
          beforePhotoTime: servicePhotos.beforePhotoTime ? utils.formatNormalDate(servicePhotos.beforePhotoTime) : null,
          afterPhotoTime: servicePhotos.afterPhotoTime ? utils.formatNormalDate(servicePhotos.afterPhotoTime) : null,
        };
        this.setData({ servicePhotos: formattedPhotos });
      }
    } catch (error) {
      console.error('加载服务照片失败:', error);
      // 不显示错误提示，因为没有照片是正常情况
    }
  },

  // 预览服务照片
  previewServicePhoto(e) {
    const { url, type } = e.currentTarget.dataset;
    const { servicePhotos } = this.data;

    if (!servicePhotos || !url || !type) return;

    // 根据类型获取对应的照片数组
    const photos = type === 'before' ? servicePhotos.beforePhotos : servicePhotos.afterPhotos;

    if (!photos || photos.length === 0) return;

    wx.previewImage({
      current: url,
      urls: photos
    });
  },

  // 加载照片墙数据
  async loadPhotoWallData(orderId) {
    try {
      const photoWallData = await photoWallApi.getOrderPhotos(orderId);
      if (photoWallData) {
        this.setData({ photoWallData });
      }
    } catch (error) {
      console.error('加载照片墙数据失败:', error);
      // 不显示错误提示，因为没有照片墙数据是正常情况
    }
  },

  // 查看照片墙详情
  viewPhotoWallDetail() {
    const { photoWallData } = this.data;
    if (!photoWallData || !photoWallData.id) {
      wx.showToast({
        title: '暂无照片墙数据',
        icon: 'none'
      });
      return;
    }

    wx.navigateTo({
      url: `/pages/photoWall/detail?id=${photoWallData.id}`,
      fail: (err) => {
        console.error('页面跳转失败:', err);
        wx.showToast({
          title: '页面跳转失败',
          icon: 'none'
        });
      }
    });
  },

  // 申请追加服务
  applyAdditionalService(e) {
    const { orderDetailId } = e.currentTarget.dataset;
    const { orderDetail } = this.data;

    if (!orderDetail || orderDetail.status !== OrderStatus.服务中) {
      wx.showToast({
        title: '当前订单状态不支持申请追加服务',
        icon: 'none'
      });
      return;
    }

    wx.navigateTo({
      url: `/pages/additionalService/apply/index?orderDetailId=${orderDetailId}`
    });
  },

  // 加载追加服务列表
  async loadAdditionalServices(orderDetailId) {
    try {
      const services = await additionalServiceApi.list(orderDetailId);

      // 格式化数据
      const formattedServices = (services || []).map(item => ({
        ...item,
        createdAt: item.createdAt ? utils.formatNormalDate(item.createdAt) : '',
        confirmTime: item.confirmTime ? utils.formatNormalDate(item.confirmTime) : '',
        statusInfo: this.data.additionalServiceStatusMap[item.status] || {
          text: item.status,
          color: '#999',
          desc: '',
          actions: ['view']
        }
      }));

      this.setData({
        additionalServices: formattedServices
      });

    } catch (error) {
      console.error('加载追加服务列表失败:', error);
      // 不显示错误提示，因为没有追加服务是正常情况
      // 但设置空数组确保UI状态正确
      this.setData({
        additionalServices: []
      });
    }
  },

  // 查看追加服务详情
  viewAdditionalServiceDetail(e) {
    const { id } = e.currentTarget.dataset;
    const { orderDetail } = this.data;

    if (!orderDetail.orderDetails || orderDetail.orderDetails.length === 0) return;

    const orderDetailId = orderDetail.orderDetails[0].id;

    wx.navigateTo({
      url: `/pages/additionalService/detail/index?orderDetailId=${orderDetailId}&id=${id}`
    });
  },

  // 支付追加服务 - 直接在当前页面处理支付
  payAdditionalService(e) {
    const { id } = e.currentTarget.dataset;
    const { additionalServices } = this.data;

    // 找到要支付的服务
    const service = additionalServices.find(item => item.id === parseInt(id));
    if (!service) {
      wx.showToast({
        title: '服务信息不存在',
        icon: 'none',
      });
      return;
    }

    // 检查状态
    if (service.status !== 'confirmed') {
      wx.showToast({
        title: '当前状态不支持支付',
        icon: 'none',
      });
      return;
    }

    // 设置当前支付服务并显示确认模态框
    this.setData({
      currentPayService: service,
      showModal: true,
      modalTitle: '确认支付',
      modalContent: `确认支付 ¥${service.totalFee} 吗？`,
      modalButtons: [
        { text: '取消', type: 'cancel', event: 'handlePayModalCancel' },
        { text: '确认支付', type: 'primary', event: 'handlePayConfirm' },
      ],
    });
  },

  // 删除追加服务
  deleteAdditionalService(e) {
    const { id, name } = e.currentTarget.dataset;

    wx.showModal({
      title: '删除确认',
      content: `确定要删除追加服务"${name}"吗？`,
      success: (res) => {
        if (res.confirm) {
          this.performDeleteAdditionalService(id);
        }
      }
    });
  },

  // 执行删除追加服务
  async performDeleteAdditionalService(id) {
    try {
      wx.showLoading({ title: '删除中...' });

      const { orderDetail, userInfo } = this.data;
      if (!orderDetail.orderDetails || orderDetail.orderDetails.length === 0) {
        throw new Error('订单详情不存在');
      }

      if (!userInfo || !userInfo.id) {
        throw new Error('用户信息不存在');
      }

      const orderDetailId = orderDetail.orderDetails[0].id;

      // 调用删除API，传递customerId
      await additionalServiceApi.delete(orderDetailId, id, userInfo.id);

      // 重新加载追加服务列表
      await this.loadAdditionalServices(orderDetailId);

      wx.showToast({
        title: '删除成功',
        icon: 'success'
      });

    } catch (error) {
      console.error('删除追加服务失败:', error);

      // 根据错误类型显示不同的提示信息
      let errorMessage = '删除失败';
      if (error.message && error.message.includes('待确认')) {
        errorMessage = '只能删除待确认状态的申请';
      } else if (error.message && error.message.includes('权限')) {
        errorMessage = '无权限删除此申请';
      }

      wx.showToast({
        title: errorMessage,
        icon: 'none'
      });
    } finally {
      wx.hideLoading();
    }
  },

  /**
   * 处理追加服务支付确认
   */
  async handlePayConfirm() {
    this.setData({ showModal: false });

    // 先同步支付状态，检查是否已经支付
    const { currentPayService } = this.data;
    if (currentPayService) {
      wx.showLoading({ title: '检查支付状态...' });

      const syncResult = await this.syncAdditionalServicePaymentStatus(currentPayService.id);
      wx.hideLoading();

      if (syncResult) {
        // 同步成功，检查同步结果
        const { additionalServices } = this.data;
        const updatedService = additionalServices.find(item => item.id === currentPayService.id);

        if (updatedService && updatedService.status === 'paid') {
          // 已经是已支付状态，直接显示成功并刷新
          this.setData({ currentPayService: null });
          this.showAdditionalServicePaymentSuccessModal();
          return;
        }
      }
    }

    // 如果未支付或同步失败，继续正常支付流程
    await this.processAdditionalServicePay();
  },

  /**
   * 处理追加服务支付
   */
  async processAdditionalServicePay() {
    const { userInfo, orderDetail, currentPayService } = this.data;

    if (!currentPayService || this.data.paying) return;

    if (!orderDetail.orderDetails || orderDetail.orderDetails.length === 0) {
      wx.showToast({
        title: '订单信息不完整',
        icon: 'none',
      });
      return;
    }

    const orderDetailId = orderDetail.orderDetails[0].id;

    try {
      this.setData({ paying: true });

      // 处理0元订单
      if (Number(currentPayService.totalFee) === 0) {
        wx.showLoading({ title: '处理中...' });
        const result = await additionalServiceApi.pay(orderDetailId, currentPayService.id, userInfo.id);
        wx.hideLoading();

        if (result) {
          // 0元订单支付成功后刷新数据并显示成功
          await this.refreshOrderData();
          this.showAdditionalServicePaymentSuccessModal();
        } else {
          wx.showToast({ title: '支付失败，请重试', icon: 'none' });
        }
        return;
      }

      // 正常支付流程
      wx.showLoading({ title: '发起支付...' });
      const payResult = await additionalServiceApi.pay(orderDetailId, currentPayService.id, userInfo.id);
      wx.hideLoading();

      if (payResult && payResult.orderSn) {
        // 调用微信支付
        const _this = this;
        payApi.doPay({
          sn: payResult.orderSn,
          onOk: async () => {
            // 支付成功后刷新数据并显示成功
            await _this.refreshOrderData();
            _this.showAdditionalServicePaymentSuccessModal();
          },
          onCancel: () => wx.showToast({ title: '取消支付', icon: 'none' }),
          onError: () => wx.showToast({ title: '支付失败', icon: 'none' }),
        });
      } else {
        wx.showToast({ title: '支付失败，请重试', icon: 'none' });
      }

    } catch (error) {
      wx.hideLoading();
      console.error('追加服务支付失败:', error);
      wx.showToast({ title: '支付失败，请重试', icon: 'none' });
    } finally {
      this.setData({ paying: false });
    }
  },

  /**
   * 显示追加服务支付成功模态框
   */
  showAdditionalServicePaymentSuccessModal() {
    this.setData({
      showModal: true,
      modalTitle: '支付成功',
      modalContent: '您的追加服务已支付成功，服务人员将为您提供服务',
      modalButtons: [{ text: '确定', type: 'primary', event: 'handleAdditionalServicePaymentSuccess' }],
    });
  },

  /**
   * 刷新订单数据 - 从服务器重新获取最新数据
   */
  async refreshOrderData() {
    const { orderDetail, userInfo } = this.data;
    if (orderDetail && orderDetail.id && userInfo && userInfo.id) {
      try {
        wx.showLoading({ title: '刷新中...' });

        // 从服务器重新获取订单详情
        const latestOrderDetail = await orderApi.getDetail(userInfo.id, orderDetail.id);

        if (latestOrderDetail) {
          // 使用最新的订单数据重新加载页面
          const orderData = JSON.stringify(latestOrderDetail);
          this.loadOrders(orderData);
        }

        wx.hideLoading();
      } catch (error) {
        wx.hideLoading();
        console.error('刷新订单数据失败:', error);
        // 刷新失败时，仍然重新加载追加服务列表
        if (orderDetail.orderDetails && orderDetail.orderDetails.length > 0) {
          this.loadAdditionalServices(orderDetail.orderDetails[0].id);
        }
      }
    }
  },

  /**
   * 检查是否可以修改服务地址
   */
  canModifyServiceAddress() {
    const { orderDetail } = this.data;
    if (!orderDetail || !orderDetail.status) {
      return false;
    }

    // 只有在待付款、待接单、待服务状态下才能修改服务地址
    const allowedStatuses = [
      OrderStatus.待付款,
      OrderStatus.待接单,
      OrderStatus.待服务
    ];

    return allowedStatuses.includes(orderDetail.status);
  },

  /**
   * 修改服务地址
   */
  modifyServiceAddress() {
    const { orderDetail } = this.data;

    if (!this.canModifyServiceAddress()) {
      wx.showToast({
        title: '当前订单状态不允许修改服务地址',
        icon: 'none'
      });
      return;
    }

    // 显示地址修改弹窗
    this.setData({
      showAddressModal: true,
      addressForm: {
        address: orderDetail.address || '',
        addressDetail: orderDetail.addressDetail || '',
        addressRemark: orderDetail.addressRemark || '',
        longitude: orderDetail.longitude || null,
        latitude: orderDetail.latitude || null,
        addressId: orderDetail.addressId || null
      }
    });

    // 加载用户地址列表
    this.loadUserAddressList();
  },

  /**
   * 同步追加服务支付状态
   * @param {number} serviceId 追加服务ID
   * @param {boolean} autoRefresh 是否自动刷新数据，默认true
   */
  async syncAdditionalServicePaymentStatus(serviceId, autoRefresh = true) {
    const { orderDetail, userInfo } = this.data;

    if (!orderDetail.orderDetails || orderDetail.orderDetails.length === 0) {
      console.error('订单详情不存在，无法同步支付状态');
      return false;
    }

    const orderDetailId = orderDetail.orderDetails[0].id;

    try {
      console.log('开始同步追加服务支付状态...');
      const syncResult = await additionalServiceApi.syncPaymentStatus(orderDetailId, serviceId, userInfo.id);

      if (syncResult && syncResult.success) {
        console.log('支付状态同步成功:', syncResult.message);

        // 根据参数决定是否自动刷新订单数据
        if (autoRefresh) {
          await this.refreshOrderData();
        }

        return syncResult;
      } else {
        console.log('支付状态同步失败:', syncResult?.message || '未知错误');
        return false;
      }
    } catch (error) {
      console.error('同步支付状态异常:', error);
      return false;
    }
  },

  /**
   * 处理追加服务支付成功
   */
  async handleAdditionalServicePaymentSuccess() {
    this.setData({
      showModal: false,
      currentPayService: null
    });

    // 点击确定按钮后刷新详情页面
    await this.refreshOrderData();
  },

  /**
   * 加载用户地址列表
   */
  async loadUserAddressList() {
    try {
      const { userInfo } = this.data;

      if (!userInfo) {
        wx.showToast({
          title: '请先登录',
          icon: 'none'
        });
        return;
      }

      if (!userInfo.id) {
        wx.showToast({
          title: '用户信息异常',
          icon: 'none'
        });
        return;
      }

      const addressList = await addressApi.list(userInfo.id);



      this.setData({ userAddressList: addressList || [] });
    } catch (error) {
      console.error('加载地址列表失败:', error);
      wx.showToast({
        title: '加载地址列表失败',
        icon: 'none'
      });
    }
  },

  /**
   * 选择地址类型
   */
  selectAddressType(e) {
    const { type } = e.currentTarget.dataset;
    this.setData({ selectedAddressType: type });
  },

  /**
   * 选择已保存的地址
   */
  selectSavedAddress(e) {
    const { address } = e.currentTarget.dataset;



    // 如果点击的是已选中的地址，则取消选择
    if (this.data.addressForm.addressId === address.id) {
      this.setData({
        'addressForm.address': '',
        'addressForm.addressDetail': '',
        'addressForm.addressRemark': '',
        'addressForm.longitude': null,
        'addressForm.latitude': null,
        'addressForm.addressId': null
      });
      return;
    }

    // 选择新地址 - 根据实际字段名称获取经纬度，并转换为数字类型
    const longitude = address.longitude ? parseFloat(address.longitude) :
                     address.lng ? parseFloat(address.lng) :
                     address.lon ? parseFloat(address.lon) : null;
    const latitude = address.latitude ? parseFloat(address.latitude) :
                    address.lat ? parseFloat(address.lat) : null;



    this.setData({
      'addressForm.address': address.addressText,
      'addressForm.addressDetail': address.detailAddress,
      'addressForm.addressRemark': address.remark || '',
      'addressForm.longitude': longitude,
      'addressForm.latitude': latitude,
      'addressForm.addressId': address.id,
      selectedAddressType: 'saved'
    });

    // 根据是否有经纬度给出不同的提示
    if (longitude && latitude) {
      wx.showToast({
        title: '地址已选择',
        icon: 'success',
        duration: 1000
      });
    } else {
      this.showLocationMissingTip();
    }
  },

  /**
   * 显示缺少定位信息的提示
   */
  showLocationMissingTip() {
    wx.showModal({
      title: '定位信息缺失',
      content: '该地址缺少精确定位信息，建议前往地址管理界面使用地图点选以补全定位信息，方便上门服务。',
      showCancel: true,
      cancelText: '继续使用',
      confirmText: '去补全',
      success: (res) => {
        if (res.confirm) {
          // 跳转到地址管理页面
          wx.navigateTo({
            url: '/pages/service/adress/index'
          });
        }
      }
    });
  },

  /**
   * 地址输入
   */
  onAddressInput(e) {
    this.setData({
      'addressForm.address': e.detail.value,
      'addressForm.addressId': null, // 手动输入时清除地址ID
      'addressForm.longitude': null, // 手动输入时清除经纬度
      'addressForm.latitude': null
    });
  },

  /**
   * 详细地址输入
   */
  onAddressDetailInput(e) {
    this.setData({
      'addressForm.addressDetail': e.detail.value
    });
  },

  /**
   * 地址备注输入
   */
  onAddressRemarkInput(e) {
    this.setData({
      'addressForm.addressRemark': e.detail.value
    });
  },

  /**
   * 选择地图位置
   */
  async chooseMapLocation() {
    try {
      const location = await wx.chooseLocation();
      this.setData({
        'addressForm.address': location.address,
        'addressForm.addressDetail': location.name,
        'addressForm.longitude': location.longitude,
        'addressForm.latitude': location.latitude,
        'addressForm.addressId': null,
        selectedAddressType: 'manual'
      });
    } catch (error) {
      if (error.errMsg !== 'chooseLocation:fail cancel') {
        wx.showToast({
          title: '选择位置失败',
          icon: 'none'
        });
      }
    }
  },

  /**
   * 关闭地址修改弹窗
   */
  closeAddressModal() {
    this.setData({ showAddressModal: false });
  },

  /**
   * 确认修改地址
   */
  async confirmModifyAddress() {
    const { addressForm, orderDetail, userInfo } = this.data;

    // 验证必填字段
    if (!addressForm.address.trim()) {
      wx.showToast({
        title: '请输入服务地址',
        icon: 'none'
      });
      return;
    }

    if (!addressForm.addressDetail.trim()) {
      wx.showToast({
        title: '请输入详细地址',
        icon: 'none'
      });
      return;
    }

    try {
      this.setData({ addressModifying: true });
      wx.showLoading({ title: '修改中...' });

      // 准备提交数据
      const submitData = {
        address: addressForm.address.trim(),
        addressDetail: addressForm.addressDetail.trim(),
        longitude: addressForm.longitude,
        latitude: addressForm.latitude,
        addressRemark: addressForm.addressRemark.trim(),
        addressId: addressForm.addressId,
        userType: 'customer'
      };

      // 调试信息：显示提交的数据
      console.log('提交的地址数据:', {
        address: submitData.address,
        addressDetail: submitData.addressDetail,
        longitude: submitData.longitude,
        latitude: submitData.latitude,
        addressId: submitData.addressId,
        hasCoordinates: !!(submitData.longitude && submitData.latitude)
      });

      // 调用修改服务地址API
      await orderApi.updateServiceAddress(orderDetail.id, submitData);

      wx.hideLoading();
      wx.showToast({
        title: '修改成功',
        icon: 'success'
      });

      // 关闭弹窗并刷新数据
      this.setData({ showAddressModal: false });
      await this.refreshOrderData();

    } catch (error) {
      wx.hideLoading();
      console.error('修改服务地址失败:', error);
      wx.showToast({
        title: error.message || '修改失败，请重试',
        icon: 'none'
      });
    } finally {
      this.setData({ addressModifying: false });
    }
  },

  /**
   * 处理支付模态框取消
   */
  handlePayModalCancel() {
    this.setData({
      showModal: false,
      currentPayService: null
    });
  },
});
