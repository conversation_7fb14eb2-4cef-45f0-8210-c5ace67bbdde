.container {
  background-color: #f4f4f4;
  padding: 20rpx 20rpx 100rpx;
  font-family: Arial, sans-serif;
}

.order-header {
  text-align: center;
  padding: 20rpx;
  background-color: #fff;
  margin-bottom: 20rpx;
}

.title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.order-info {
  background-color: #fff;
  padding: 20rpx;
  border-radius: 10rpx;
  margin-bottom: 20rpx;
}

.order-content {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}

.product-image {
  width: 150rpx;
  height: 150rpx;
  margin-right: 20rpx;
  border-radius: 10rpx;
}

.product-info {
  display: flex;
  flex-direction: column;
  flex: 1;
}

.product-service {
  background: rgba(255, 67, 145, 0.1);
  border-radius: 8px;
  padding: 8rpx 12rpx;
  width: 70%;
  line-height: 50rpx;
}

.product-name {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 10rpx;
}

.product-quantity {
  font-size: 24rpx;
  color: #999;
}

.paid-money {
  text-align: right;
  font-size: 32rpx;
  color: #333;
  font-weight: bold;
}

.magin-bottom {
  margin-bottom: 20rpx;
}

.order-details {
  border-top: 1rpx solid #eee;
  padding-top: 20rpx;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  align-items: baseline;
  line-height: 60rpx;
  gap: 50rpx;
}

.address-content {
  display: flex;
  align-items: baseline;
  flex: 1;
  justify-content: space-between;
  gap: 20rpx;
}

.edit-btn {
  display: flex;
  align-items: center;
  gap: 6rpx;
  font-size: 22rpx;
  color: #007aff;
  padding: 10rpx 16rpx;
  border: none;
  border-radius: 24rpx;
  background: linear-gradient(135deg, rgba(0, 122, 255, 0.08), rgba(0, 122, 255, 0.12));
  backdrop-filter: blur(10rpx);
  box-shadow: 0 2rpx 12rpx rgba(0, 122, 255, 0.15);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  font-weight: 500;
  min-width: 80rpx;
  justify-content: center;
  position: relative;
  overflow: hidden;
}

.edit-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transition: left 0.5s ease;
}

.edit-btn:active::before {
  left: 100%;
}

.edit-btn:active {
  transform: scale(0.96);
  background: linear-gradient(135deg, rgba(0, 122, 255, 0.15), rgba(0, 122, 255, 0.2));
  box-shadow: 0 4rpx 16rpx rgba(0, 122, 255, 0.25);
}

.edit-text {
  font-size: 22rpx;
  font-weight: 500;
  line-height: 1;
}

.label {
  font-size: 28rpx;
  color: #666;
  min-width: 200rpx;
}
.content{
  text-align: right;
}
.flex1-clz {
  border-top: 2rpx solid #e4e4e4;
  padding: 20rpx 32rpx 24rpx;
  border-radius: 24rpx 24rpx 0rpx 0rpx;
  bottom: 0rpx;
  background-color: #ffffff;
  box-shadow: 0rpx -2rpx 20rpx rgba(0, 0, 0, 0.08);
  overflow: visible;
  left: 0rpx;
}
.order-actions {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  gap: 16rpx;
}

.action-btn {
  background: rgba(238, 238, 238, 1);
  color: rgba(51, 51, 51, 1);
  font-weight: 500;
  min-width: 140rpx;
  text-align: center;
  white-space: nowrap;

  font-size: 24rpx;
  padding: 8rpx 16rpx;
  border-radius: 16rpx;
  border: 1rpx solid;
  transition: all 0.3s ease;
}

.action-btn:nth-last-child(1) {
  background: rgba(47, 131, 255, 0.2);
  color: rgba(47, 131, 255, 1);
  border-radius: 40rpx;
}

/* 蓝色按钮样式 - 主要操作 */
.action-btn.blue-btn {
  background: linear-gradient(135deg, #4A90E2 0%, #357ABD 100%);
  color: #ffffff;
  border: 1rpx solid #4A90E2;
  box-shadow: 0 4rpx 12rpx rgba(74, 144, 226, 0.3);
}

.action-btn.blue-btn:active {
  background: linear-gradient(135deg, #357ABD 0%, #2E6BA8 100%);
  transform: translateY(1rpx);
  box-shadow: 0 2rpx 8rpx rgba(74, 144, 226, 0.4);
}

/* 灰色按钮样式 - 次要操作 */
.action-btn.gray-btn {
  background: #ffffff;
  color: #666666;
  border: 1rpx solid #e0e0e0;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.08);
}

.action-btn.gray-btn:active {
  background: #f8f8f8;
  border-color: #d0d0d0;
  transform: translateY(1rpx);
}
.more-btn {
  visibility: hidden;
  margin-right: 20rpx;
  font-size: 32rpx;
  position: relative;
  color: rgba(102, 102, 102, 1);
}
.more-actions-dropdown {
  position: absolute;
  bottom: 110rpx;
  left: 0;
  width: 260rpx;
  background-color: #fff;
  border-radius: 10rpx;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  z-index: 100;
  overflow: hidden;
}

.dropdown-item {
  padding: 20rpx;
  text-align: center;
  font-size: 24rpx;
  color: #333;
  border-bottom: 1rpx solid #eee;
  background-color: #fff;
  transition: background-color 0.3s;
}

.dropdown-item:last-child {
  border-bottom: none;
}

.dropdown-item:hover {
  background-color: #f4f4f4;
}

/* 服务照片样式 */
.service-photos {
  margin-bottom: 20rpx;
}

.photo-section {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.05);
}

.section-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
  padding-bottom: 16rpx;
  border-bottom: 2rpx solid #f0f0f0;
}

.title-text {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.photo-time {
  font-size: 24rpx;
  color: #999;
}

.photo-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
}

.photo-item {
  width: 200rpx;
  height: 200rpx;
  border-radius: 12rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.photo-preview {
  width: 100%;
  height: 100%;
  border-radius: 12rpx;
}

/* 照片墙入口样式 */
.photo-wall-entry-section {
  margin-bottom: 20rpx;
}

.photo-wall-card {
  background: linear-gradient(135deg, #ff438f 0%, #ff6ba8 100%);
  border-radius: 16rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 12rpx rgba(255, 67, 143, 0.2);
  transition: all 0.3s ease;
}

.photo-wall-card:active {
  transform: scale(0.98);
  box-shadow: 0 2rpx 8rpx rgba(255, 67, 143, 0.15);
}

.photo-wall-header {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}

.photo-wall-icon {
  font-size: 40rpx;
  margin-right: 20rpx;
}

.photo-wall-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.photo-wall-title {
  color: #fff;
  font-size: 32rpx;
  font-weight: bold;
}

.photo-wall-desc {
  color: rgba(255, 255, 255, 0.8);
  font-size: 24rpx;
}

.arrow-right {
  color: rgba(255, 255, 255, 0.8);
  font-size: 28rpx;
}

.photo-wall-stats {
  display: flex;
  gap: 30rpx;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.stat-icon {
  font-size: 24rpx;
}

.stat-text {
  color: rgba(255, 255, 255, 0.9);
  font-size: 24rpx;
}

/* 追加服务样式 */
.additional-services-section {
  margin-bottom: 20rpx;
}

.additional-services-section .section-title {
  background-color: #fff;
  border-radius: 16rpx 16rpx 0 0;
  padding: 30rpx 30rpx 20rpx;
  margin-bottom: 0;
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  border-bottom: 2rpx solid #f0f0f0;
}

.additional-services-list {
  background-color: #fff;
  border-radius: 0 0 16rpx 16rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.05);
}

.additional-service-card {
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.additional-service-card:last-child {
  border-bottom: none;
}

.service-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20rpx;
}

.service-info {
  flex: 1;
}

.service-name {
  display: block;
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 8rpx;
}

.service-time {
  font-size: 24rpx;
  color: #999;
}

.service-status {
  font-size: 26rpx;
  font-weight: bold;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  background-color: rgba(255, 149, 0, 0.1);
  white-space: nowrap;
}

.service-price {
  display: flex;
  justify-content: space-between;
  margin-bottom: 24rpx;
  padding: 20rpx;
  background-color: #f8f9fa;
  border-radius: 12rpx;
}

.price-item {
  display: flex;
  align-items: center;
}

.price-label {
  font-size: 26rpx;
  color: #666;
  margin-right: 8rpx;
}

.price-value {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
}

.price-value.highlight {
  color: #ff4d4f;
  font-size: 32rpx;
}

.service-actions {
  display: flex;
  justify-content: flex-end;
  gap: 16rpx;
}

.action-btn-small {
  min-width: 100rpx;
}

.btn-outline {
  padding: 12rpx 24rpx;
  border: 1rpx solid #d9d9d9;
  border-radius: 20rpx;
  background-color: #fff;
  color: #666;
  font-size: 24rpx;
  text-align: center;
  transition: all 0.3s ease;
}

.btn-outline:active {
  background-color: #f5f5f5;
  border-color: #bfbfbf;
}

.btn-primary {
  padding: 12rpx 24rpx;
  border: 1rpx solid #1890ff;
  border-radius: 20rpx;
  background-color: #1890ff;
  color: #fff;
  font-size: 24rpx;
  text-align: center;
  transition: all 0.3s ease;
}

.btn-primary:active {
  background-color: #096dd9;
  border-color: #096dd9;
}

.btn-danger {
  padding: 12rpx 24rpx;
  border: 1rpx solid #ff4d4f;
  border-radius: 20rpx;
  background-color: #fff;
  color: #ff4d4f;
  font-size: 24rpx;
  text-align: center;
  transition: all 0.3s ease;
}

.btn-danger:active {
  background-color: #fff2f0;
  border-color: #ff7875;
}

/* 双按钮布局样式 */
.dual-button-layout {
  display: flex;
  gap: 20rpx;
  padding: 0;
  align-items: center;
  min-height: 88rpx;
}

/* 当只有一个按钮时，让它占满整个宽度 */
.dual-button-layout > view:only-child {
  flex: 1;
}

/* 操作按钮样式 */
.modern-action-btn {
  flex: 1;
  display: flex;
  gap: 8rpx;
  align-items: center;
  justify-content: center;
  padding: 20rpx 20rpx;
  border-radius: 50rpx;
  transition: all 0.3s ease;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.15);
  position: relative;
  overflow: hidden;
}

.modern-action-btn:active {
  transform: scale(0.95);
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.2);
}

/* 联系服务人员按钮 - 粉色渐变 */
.modern-action-btn.contact-btn {
  background: linear-gradient(135deg, #ff6b9d, #ff4081);
  color: white;
}

/* 去付款按钮 - 绿色渐变 (重要操作) */
.modern-action-btn.pay-btn {
  background: linear-gradient(135deg, #52c41a, #389e0d);
  color: white;
}

/* 催接单按钮 - 橙色渐变 (提醒类操作) */
.modern-action-btn.urge-btn {
  background: linear-gradient(135deg, #fa8c16, #d46b08);
  color: white;
}

/* 申请追加服务按钮 - 蓝色渐变 (主要操作) */
.modern-action-btn.additional-service-btn {
  background: linear-gradient(135deg, #1890ff, #096dd9);
  color: white;
}

/* 去评价按钮 - 紫色渐变 (完成类操作) */
.modern-action-btn.review-btn {
  background: linear-gradient(135deg, #722ed1, #531dab);
  color: white;
}

/* 取消订单按钮 - 灰色渐变 (次要操作) */
.modern-action-btn.cancel-btn {
  background: linear-gradient(135deg, #8c8c8c, #595959);
  color: white;
}

/* 按钮激活状态的特殊效果 */
.modern-action-btn.contact-btn:active {
  background: linear-gradient(135deg, #ff4081, #e91e63);
}

.modern-action-btn.pay-btn:active {
  background: linear-gradient(135deg, #389e0d, #237804);
}

.modern-action-btn.urge-btn:active {
  background: linear-gradient(135deg, #d46b08, #ad4e00);
}

.modern-action-btn.additional-service-btn:active {
  background: linear-gradient(135deg, #096dd9, #0050b3);
}

.modern-action-btn.review-btn:active {
  background: linear-gradient(135deg, #531dab, #391085);
}

.modern-action-btn.cancel-btn:active {
  background: linear-gradient(135deg, #595959, #434343);
}

/* 按钮图标和文字样式 */
.btn-icon {
  font-size: 36rpx;
  filter: drop-shadow(0 2rpx 4rpx rgba(0, 0, 0, 0.2));
  width: 36rpx;
  height: 36rpx;
}

.btn-text {
  font-size: 28rpx;
  font-weight: 500;
  line-height: 1;
}

/* 修改地址按钮样式 */
.modify-address-btn {
  color: #007aff;
  font-size: 28rpx;
  padding: 0;
  border: none;
  border-radius: 0;
  background-color: transparent;
  margin-left: 20rpx;
  text-decoration: none;
}

/* 地址修改弹窗样式 */
.address-modify-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
}

.modal-content {
  position: relative;
  width: 680rpx;
  max-height: 80vh;
  background-color: #fff;
  border-radius: 20rpx;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 40rpx 40rpx 20rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.modal-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.modal-close {
  font-size: 48rpx;
  color: #999;
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 地址类型选择器 */
.address-type-selector {
  padding: 30rpx 40rpx 20rpx;
}

.section-label {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 20rpx;
}

.type-buttons {
  display: flex;
  gap: 20rpx;
}

.type-btn {
  flex: 1;
  height: 65rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1rpx solid #ddd;
  border-radius: 40rpx;
  font-size: 28rpx;
  color: #666;
  background-color: #fff;
}

.type-btn.active {
  background-color: #007aff;
  color: #fff;
  border-color: #007aff;
}

/* 已保存地址列表 */
.saved-address-section {
  padding: 0 40rpx 20rpx;
}

.saved-address-list {
  max-height: 400rpx;
}

/* 地址卡片样式 - 参考系统地址列表 */
.address-card {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 30rpx 20rpx;
  margin-bottom: 20rpx;
  border-radius: 12rpx;
  background-color: #ffffff;
  border: 2rpx solid #f0f0f0;
  transition: all 0.3s ease;
  position: relative;
}

.address-card:last-child {
  margin-bottom: 0;
}

.address-card.selected {
  border-color: #007aff;
  background-color: rgba(0, 122, 255, 0.05);
  box-shadow: 0 4rpx 12rpx rgba(0, 122, 255, 0.15);
}

.address-main-info {
  flex: 1;
  padding-right: 20rpx;
}

.address-title {
  font-size: 30rpx;
  color: #333;
  font-weight: 500;
  display: block;
  margin-bottom: 12rpx;
  line-height: 1.4;
}

.address-detail-text {
  font-size: 26rpx;
  color: #666;
  display: block;
  line-height: 1.3;
}

.address-tags {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 8rpx;
}

.default-badge {
  background-color: #ff4391;
  color: #fff;
  font-size: 22rpx;
  padding: 6rpx 12rpx;
  border-radius: 12rpx;
  font-weight: 500;
}

.coordinate-badge {
  background-color: #52c41a;
  color: #fff;
  font-size: 20rpx;
  padding: 4rpx 8rpx;
  border-radius: 10rpx;
  font-weight: 500;
  margin-top: 4rpx;
}

.selected-badge {
  background-color: #007aff;
  color: #fff;
  font-size: 24rpx;
  width: 40rpx;
  height: 40rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2rpx 8rpx rgba(0, 122, 255, 0.3);
}

.selected-icon {
  font-size: 20rpx;
  font-weight: bold;
}

/* 空状态样式 */
.empty-address-state {
  text-align: center;
  padding: 80rpx 40rpx;
  color: #999;
}

.empty-icon {
  font-size: 80rpx;
  margin-bottom: 20rpx;
  opacity: 0.6;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
  display: block;
  margin-bottom: 12rpx;
}

.empty-tip {
  font-size: 24rpx;
  color: #ccc;
  display: block;
}

/* 地图选择区域 */
.map-select-section {
  padding: 0 40rpx 20rpx;
}

.map-select-card {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 30rpx 20rpx;
  border-radius: 12rpx;
  background-color: #ffffff;
  border: 2rpx dashed #007aff;
  transition: all 0.3s ease;
}

.map-select-card:active {
  background-color: rgba(0, 122, 255, 0.05);
  transform: scale(0.98);
}

.map-select-content {
  display: flex;
  align-items: center;
  flex: 1;
}

.map-icon-wrapper {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  background-color: rgba(0, 122, 255, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20rpx;
}

.map-icon {
  font-size: 32rpx;
}

.map-text-wrapper {
  flex: 1;
}

.map-title {
  font-size: 30rpx;
  color: #007aff;
  font-weight: 500;
  display: block;
  margin-bottom: 6rpx;
}

.map-subtitle {
  font-size: 24rpx;
  color: #999;
  display: block;
}

.map-arrow {
  font-size: 32rpx;
  color: #007aff;
  font-weight: bold;
}

/* 表单区域 */
.form-section {
  padding: 20rpx 40rpx;
  flex: 1;
  overflow-y: auto;
}

.form-item {
  margin-bottom: 30rpx;
  position: relative;
}

.form-label {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 12rpx;
  display: block;
}

.form-input {
  width: 100%;
  height: 80rpx;
  border: 1rpx solid #ddd;
  border-radius: 8rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
  background-color: #fff;
  box-sizing: border-box;
}

.form-textarea {
  width: 100%;
  min-height: 120rpx;
  border: 1rpx solid #ddd;
  border-radius: 8rpx;
  padding: 20rpx;
  font-size: 28rpx;
  background-color: #fff;
  box-sizing: border-box;
}

.char-count {
  position: absolute;
  right: 20rpx;
  bottom: 8rpx;
  font-size: 24rpx;
  color: #999;
}

/* 经纬度信息显示 */
.coordinate-info {
  margin-top: 20rpx;
  padding: 20rpx;
  background-color: #f8f9fa;
  border-radius: 8rpx;
  border-left: 4rpx solid #007aff;
}

.coordinate-label {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 8rpx;
}

.coordinate-text {
  font-size: 24rpx;
  color: #999;
  font-family: monospace;
}

/* 底部按钮 */
.modal-footer {
  display: flex;
  gap: 20rpx;
  padding: 30rpx 40rpx;
  border-top: 1rpx solid #f0f0f0;
}

.cancel-btn {
  flex: 1;
  border: 1rpx solid #ddd;
  border-radius: 40rpx;
  background-color: #fff;
  color: #666;
  font-size: 28rpx;
}

.confirm-btn {
  flex: 1;
  border: none;
  border-radius: 40rpx;
  background-color: #007aff;
  color: #fff;
  font-size: 28rpx;
}

.confirm-btn.loading {
  background-color: #ccc;
  color: #999;
}
