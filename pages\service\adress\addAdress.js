// pages/service/adress/addAdress.js
import addressApi from "../../../api/modules/address";
import siteinfo from "../../../siteinfo";
import { Validate } from "../../../common/Page";
import Session from "../../../common/Session";
Page({

  /**
   * 页面的初始数据
   */
  data: {
    siteinfo,
    isUse: false,
    form: {}
  },
  onLoad(options) {
    const {
      actionType,
      data,
      isUse
    } = options;

    // 获取用户信息，优先从data中获取，如果没有则从Session中获取
    let userInfo = this.data.userInfo || Session.getUser();

    if (isUse) {
      this.setData({
        isUse: isUse == "true"
      })
    }

    if (actionType === 'edit') {
      // 使用接收到的对象更新页面数据
      const {
        provinceName,
        cityName,
        districtName,
        ...rest
      } = JSON.parse(data);
      this.setData({
        form: {
          ...rest,
          addressArea: provinceName + ',' + cityName + ',' + districtName
        }
      });
    } else {
      this.setData({
        form: {
          customerId: userInfo ? userInfo.id : null
        }
      });
    }

    // 确保userInfo被设置到data中
    if (userInfo) {
      this.setData({
        userInfo: userInfo
      });
    }
  },
  // 统一的表单字段更新方法
  updateFormField(field, value) {
    this.setData({
      [`form.${field}`]: value,
    });
  },

  // 表单输入变化处理方法
  changeValue(e) {
    const { key } = e.currentTarget.dataset;
    const { value } = e.detail;
    this.setData({
      [key]: value
    });
  },

  // 显示提示信息
  showToast(title, icon = 'none') {
    wx.showToast({
      title: title,
      icon: icon
    });
  },
  changeFormSwitched(evt) {
    let checked = !evt.currentTarget.dataset.checked;
    this.setData({
      'form.isDefault': checked
    });
  },
  // 地理定位
  async getLocation() {
    wx.getSetting({
      success: async (res) => {
        if (!res.authSetting["scope.userLocation"]) {
          wx.authorize({
            scope: "scope.userLocation",
            success: () => this.getActualLocation(),
            fail: () => {
              wx.showModal({
                title: '位置权限',
                content: '需要获取您的位置权限来选择地址，请在设置中开启',
                showCancel: true,
                cancelText: '取消',
                confirmText: '去设置',
                success: (modalRes) => {
                  if (modalRes.confirm) {
                    wx.openSetting();
                  }
                }
              });
            },
          });
        } else {
          this.getActualLocation();
        }
      },
    });
  },
  async getActualLocation() {
    try {
      wx.showLoading({
        title: '打开地图中...'
      });

      // 打开地图选择位置，获取 纬度 、精度
      const location = await wx.chooseLocation();
      wx.hideLoading();

      if (location) {
        // 使用微信返回的地址信息，同时调用高德API获取更详细的信息
        this.setData({
          'form.addressText': location.address || location.name,
          'form.longitude': location.longitude,
          'form.latitude': location.latitude
        });

        console.log('微信地图选择结果:', {
          address: location.address,
          name: location.name,
          longitude: location.longitude,
          latitude: location.latitude
        });

        // 调用高德API获取更详细的地址信息
        this._getAddressFromAMap(location.latitude, location.longitude);

        wx.showToast({
          title: '位置选择成功',
          icon: 'success'
        });
      }
    } catch (error) {
      wx.hideLoading();
      if (error.errMsg !== 'chooseLocation:fail cancel') {
        console.error('选择位置失败:', error);
        wx.showToast({
          title: '选择位置失败',
          icon: 'none'
        });
      }
    }
  },
  // 新增方法：调用高德逆地理编码API
  _getAddressFromAMap(latitude, longitude) {
    wx.request({
      url: "https://restapi.amap.com/v3/geocode/regeo",
      data: {
        key: siteinfo.constant.AMapKey,
        location: `${longitude},${latitude}`,
        radius: 1000,
        extensions: "base",
      },
      success: (res) => {
        if (res.data && res.data.status === "1") {
          const regeocode = res.data.regeocode;
          const addressComponent = regeocode.addressComponent;
          const {
            adcode,
            province,
            city,
            district,
            township,
            streetNumber
          } = addressComponent;

          const {
            street,
            number
          } = streetNumber || {};

          // 构建地址区域信息
          const addressArea = `${province},${city},${district}`;

          // 构建完整地址，优先使用高德返回的格式化地址
          let fullAddress = regeocode.formatted_address;
          if (!fullAddress) {
            // 如果没有格式化地址，则手动拼接
            fullAddress = `${province}${city}${district}${township || ''}${street || ''}${number || ''}`;
          }

          // 更新表单数据
          this.updateFormField("addressCode", adcode);
          this.updateFormField("addressArea", addressArea);
          this.updateFormField("addressText", fullAddress);
          this.updateFormField("longitude", longitude);
          this.updateFormField("latitude", latitude);

          console.log('高德地图地址解析成功:', {
            addressArea,
            fullAddress,
            longitude,
            latitude
          });
        } else {
          console.warn('高德地图API返回错误:', res.data);
        }
      },
      fail: (err) => {
        console.error("高德地图地理编码失败", err);
        // 不显示错误提示，因为已经有微信的地址信息作为备选
        console.log('使用微信地址信息作为备选');
      },
    });
  },
  async submitForm(e) {
    this.validateForm = Validate({
      contactName: {
        required: {
          message: '联系人不能为空'
        }
      },
      contactPhone: {
        required: {
          message: '手机号码不能为空'
        }
      },
      addressText: {
        required: {
          message: '请选择所在地址'
        }
      },
    });
    if (!this.validateForm.checkForm(e)) {
      let data = this.validateForm.errorList[0];
      this.showToast(data.msg, 'none');
      return false;
    } else {
      const userInfo = this.data.userInfo || Session.getUser();
      if (!userInfo) {
        this.showToast('用户信息获取失败，请重新登录', 'none');
        return;
      }
      //保存数据
      let param = e.detail.value;

      // 调试：打印表单数据，检查经纬度是否正确传递
      console.log('表单提交数据:', param);
      console.log('当前form数据:', this.data.form);

      const {
        id,
        ...rest
      } = param;

      // 确保经纬度数据正确传递
      if (this.data.form.longitude && this.data.form.latitude) {
        rest.longitude = this.data.form.longitude;
        rest.latitude = this.data.form.latitude;
      }

      console.log('最终提交到后台的数据:', rest);

      if (id === "") {
        addressApi.add(userInfo.id, {
          ...rest
        }).then((res) => {
          if (res.id) {
            if (this.data.isUse) {
              // TODO
              wx.navigateBack({
                delta: 1 // 返回上一页，相当于关闭当前页面
              })
            } else {
              wx.navigateBack({
                delta: 1 // 返回上一页，相当于关闭当前页面
              })
            }
          }
        });
      } else {
        // 确保经纬度数据正确传递（更新时）
        if (this.data.form.longitude && this.data.form.latitude) {
          rest.longitude = this.data.form.longitude;
          rest.latitude = this.data.form.latitude;
        }

        console.log('更新地址时提交到后台的数据:', rest);

        addressApi.update(userInfo.id, id, {
          ...rest
        }).then((res) => {
          if (res.id) {
            // TODO
            const curAdress = wx.getStorageSync('selectAdressInfo');
            if(curAdress && curAdress.id === res.id){
              wx.setStorageSync('selectAdressInfo', res);
            }
            if (this.data.isUse) {
              wx.navigateBack({
                delta: 1 // 返回上一页，相当于关闭当前页面
              })
            } else {
              wx.navigateBack({
                delta: 1 // 返回上一页，相当于关闭当前页面
              })
            }
          }
        });
      }
    }
  },
})