.container {
  background-color: #f1f1f1;
  min-height: 100vh;
  padding-bottom: 40rpx;
}

.order-tabs {
  display: flex;
  background-color: #fff;
  height: 120rpx;
  align-items: center;
}

.tab-item {
  flex: 1;
  text-align: center;
  color: #666;
  font-size: 28rpx;
  position: relative;
  line-height: 70rpx;
}

.tab-item.active {
  color: #333;
  font-weight: bold;
  font-size: 30rpx;
}

.tab-item.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 30rpx;
  height: 8rpx;
  border-radius: 8rpx;
  background-color: #FF4391;
}

/* 子筛选器指示器 */
.sub-filter-indicator {
  position: absolute;
  top: 50%;
  right: 10rpx;
  transform: translateY(-50%);
  transition: transform 0.3s ease;
}

.sub-filter-indicator.expanded {
  transform: translateY(-50%) rotate(180deg);
}

/* 子筛选器容器 */
.sub-filters {
  background-color: #fff;
  border-top: 1rpx solid #f0f0f0;
  padding: 20rpx;
  animation: slideDown 0.3s ease;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 子筛选器列表 */
.sub-filter-list {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
}

.sub-filter-item {
  flex: 0 0 auto;
  min-width: 120rpx;
}

/* Radio样式 */
.radio-wrapper {
  display: flex;
  align-items: center;
  padding: 12rpx 16rpx;
  border-radius: 20rpx;
  background-color: #f8f8f8;
  transition: all 0.3s ease;
}

.sub-filter-item.active .radio-wrapper {
  background-color: rgba(255, 67, 145, 0.1);
  border: 1rpx solid #FF4391;
}

.radio-circle {
  width: 32rpx;
  height: 32rpx;
  border-radius: 50%;
  border: 2rpx solid #ddd;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12rpx;
  transition: all 0.3s ease;
}

.radio-circle.checked {
  border-color: #FF4391;
  background-color: #FF4391;
}

.radio-dot {
  width: 12rpx;
  height: 12rpx;
  border-radius: 50%;
  background-color: #fff;
}

.sub-filter-text {
  font-size: 26rpx;
  color: #666;
  transition: color 0.3s ease;
}

.sub-filter-item.active .sub-filter-text {
  color: #FF4391;
  font-weight: 500;
}

.order-list {
  height: calc(100vh - 200rpx);
}

/* 当显示子筛选器时调整列表高度 */
.order-list.with-sub-filters {
  height: calc(100vh - 280rpx);
}

.order-item {
  background-color: #fff;
  margin: 20rpx;
  border-radius: 10rpx;
  position: relative;
  overflow: hidden;
}

/* 可点击的订单主体区域 */
.order-clickable-area {
  padding: 20rpx;
  padding-bottom: 10rpx;
  position: relative;
  transition: background-color 0.2s ease;
}

.order-clickable-area:active {
  background-color: #f8f8f8;
}

/* 添加点击提示 */
.order-clickable-area::after {
  content: '';
  position: absolute;
  top: 50%;
  right: 20rpx;
  transform: translateY(-50%);
  width: 12rpx;
  height: 12rpx;
  border-top: 2rpx solid #ccc;
  border-right: 2rpx solid #ccc;
  transform: translateY(-50%) rotate(45deg);
  opacity: 0.6;
}

/* 操作按钮区域 */
.order-actions-area {
  padding: 10rpx 20rpx 20rpx 20rpx;
  border-top: 1rpx solid #f0f0f0;
  background-color: #fff;
}

.order-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20rpx;
  border-bottom: 1rpx solid #eee;
  padding-bottom: 10rpx;
}

/* 订单详情信息区域 */
.order-details {
  margin-top: 10rpx;
}

.order-number {
  font-size: 24rpx;
  color: #999;
}

.order-status {
  font-size: 28rpx;
  color: #FF4391;
}

/* 头部追加服务提醒样式 */
.additional-reminder-header {
  display: flex;
  align-items: center;
  background: linear-gradient(135deg, #FF4391, #FF6B9D);
  border-radius: 12rpx;
  padding: 8rpx 16rpx;
  position: relative;
}

.additional-reminder-header::before {
  content: '';
  position: absolute;
  left: 8rpx;
  top: 50%;
  transform: translateY(-50%);
  width: 8rpx;
  height: 8rpx;
  background-color: #fff;
  border-radius: 50%;
  animation: pulse 1.5s infinite;
}

.reminder-text-header {
  font-size: 24rpx;
  color: #fff;
  font-weight: 500;
  margin-left: 18rpx;
}

/* 内联订单号样式 */
.order-number-inline {
  font-size: 24rpx;
  color: #999;
}

.order-content {
  display: flex;
  align-items: center;
  margin-bottom: 10rpx;
}

.product-image {
  width: 150rpx;
  height: 150rpx;
  margin-right: 20rpx;
  border-radius: 10rpx;
}

.product-info {
  display: flex;
  flex-direction: column;
  flex: 1;
}

.product-service {
  background: rgba(255, 67, 145, 0.1);
  border-radius: 8px;
  padding: 8rpx 12rpx;
  width: 70%;
  line-height: 50rpx;
}

.product-name {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 10rpx;
}

.product-quantity {
  font-size: 24rpx;
  color: #999;
}

.paid-money {
  text-align: right;
  font-size: 32rpx;
  color: #333;
  font-weight: bold;
}

/* 价格区域样式 */
.price-section {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
}

.additional-price-info {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  min-width: 160rpx;
}

.price-row {
  display: flex;
  align-items: center;
  margin-bottom: 8rpx;
}

.price-label {
  font-size: 24rpx;
  color: #666;
  margin-right: 8rpx;
}

.price-breakdown {
  display: flex;
  gap: 8rpx;
  margin-bottom: 8rpx;
  flex-wrap: wrap;
  justify-content: flex-end;
}

.breakdown-item {
  font-size: 20rpx;
  color: #999;
  background-color: #f5f5f5;
  padding: 4rpx 8rpx;
  border-radius: 6rpx;
  white-space: nowrap;
}

/* 追加服务提醒样式 */
.additional-reminder {
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #FF4391, #FF6B9D);
  border-radius: 10rpx;
  padding: 6rpx 12rpx;
  margin-top: 6rpx;
  position: relative;
}

.additional-reminder::before {
  content: '';
  position: absolute;
  left: 6rpx;
  top: 50%;
  transform: translateY(-50%);
  width: 8rpx;
  height: 8rpx;
  background-color: #fff;
  border-radius: 50%;
  animation: pulse 1.5s infinite;
}

.reminder-text {
  font-size: 20rpx;
  color: #fff;
  font-weight: 500;
  margin-left: 16rpx;
}

@keyframes pulse {
  0% {
    opacity: 1;
    transform: translateY(-50%) scale(1);
  }
  50% {
    opacity: 0.6;
    transform: translateY(-50%) scale(1.2);
  }
  100% {
    opacity: 1;
    transform: translateY(-50%) scale(1);
  }
}


.magin-bottom{
  margin-bottom: 20rpx;
}

.more-btn {
  visibility: hidden;
  margin-right: 20rpx;
  color: rgba(255, 67, 145, 1);
  font-size: 24rpx;
  position: relative;
}

.more-actions-dropdown {
  position: absolute;
  top: 100%;
  left: 0;
  width: 260rpx;
  background-color: #fff;
  border-radius: 10rpx;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  z-index: 100;
  overflow: hidden;
}

.dropdown-item {
  padding: 20rpx;
  text-align: center;
  font-size: 24rpx;
  color: #333;
  border-bottom: 1rpx solid #eee;
  background-color: #fff;
  transition: background-color 0.3s;
}

.dropdown-item:last-child {
  border-bottom: none;
}

.dropdown-item:hover {
  background-color: #f4f4f4;
}

.order-actions {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  flex-wrap: wrap;
  gap: 12rpx;
}

.action-btn {
  padding: 8rpx 16rpx;
  border-radius: 5rpx;
  font-size: 22rpx;
  background: rgba(238, 238, 238, 1);
  color: rgba(51, 51, 51, 1);
  border-radius: 32rpx;
  margin-left: 0;
  min-width: 120rpx;
  text-align: center;
  white-space: nowrap;
}

.action-btn:nth-last-child(1) {
  background: rgba(47, 131, 255, 0.2);
  color: rgba(47, 131, 255, 1);
  border-radius: 40rpx;
}

/* 联系员工按钮样式 */
.contact-btn {
  background: linear-gradient(135deg, #4A90E2 0%, #357ABD 100%) !important;
  color: #ffffff !important;
  border: 1rpx solid #4A90E2;
  box-shadow: 0 2rpx 8rpx rgba(74, 144, 226, 0.3);
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4rpx;
}

.contact-btn:active {
  background: linear-gradient(135deg, #357ABD 0%, #2E6BA8 100%) !important;
  transform: translateY(1rpx);
  box-shadow: 0 1rpx 4rpx rgba(74, 144, 226, 0.4);
}

.blue-btn {
  background: rgba(47, 131, 255, 0.2) !important;
  color: rgba(47, 131, 255, 1) !important;
}

/* 修改地址按钮样式 */
.address-btn {
  background: linear-gradient(135deg, #52C41A 0%, #389E0D 100%) !important;
  color: #ffffff !important;
  border: 1rpx solid #52C41A;
  box-shadow: 0 2rpx 8rpx rgba(82, 196, 26, 0.3);
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4rpx;
}

.address-btn:active {
  background: linear-gradient(135deg, #389E0D 0%, #237804 100%) !important;
  transform: translateY(1rpx);
  box-shadow: 0 1rpx 4rpx rgba(82, 196, 26, 0.4);
}

/* 修改时间按钮样式 */
.time-btn {
  background: linear-gradient(135deg, #FF9500 0%, #FF7A00 100%) !important;
  color: #ffffff !important;
  border: 1rpx solid #FF9500;
  box-shadow: 0 2rpx 8rpx rgba(255, 149, 0, 0.3);
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4rpx;
}

.time-btn:active {
  background: linear-gradient(135deg, #FF7A00 0%, #E66A00 100%) !important;
  transform: translateY(1rpx);
  box-shadow: 0 1rpx 4rpx rgba(255, 149, 0, 0.4);
}

.gray-btn {
  background: rgba(238, 238, 238, 1) !important;
  color: rgba(102, 102, 102, 1) !important;
}

.review-btn {
  background: rgba(255, 165, 0, 1) !important;
  color: white !important;
}

.empty-list {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding-top: 200rpx;
}

.empty-image {
  width: 300rpx;
  height: 300rpx;
}

.empty-text {
  margin-top: 20rpx;
  color: #999;
}

/* 模态框底部样式 */
.modal-footer {
  display: flex;
  justify-content: center;
  padding: 20rpx 0;
  border-top: 1rpx solid #eee;
  margin-top: 20rpx;
}

.agreement-link {
  color: #2F83FF;
  font-size: 28rpx;
  text-decoration: underline;
  padding: 10rpx 20rpx;
}

/* 用户备注预览样式 */
.user-remark-preview {
  color: #666;
  font-size: 24rpx;
  line-height: 1.4;
  max-width: 100%;
  word-break: break-all;
}

/* 地址修改弹窗样式 */
.address-modify-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
}

.modal-content {
  position: relative;
  width: 680rpx;
  max-height: 80vh;
  background-color: #fff;
  border-radius: 20rpx;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 40rpx 40rpx 20rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.modal-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.modal-close {
  font-size: 48rpx;
  color: #999;
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 地址类型选择器 */
.address-type-selector {
  padding: 30rpx 40rpx 20rpx;
}

.section-label {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 20rpx;
}

.type-buttons {
  display: flex;
  gap: 20rpx;
}

.type-btn {
  flex: 1;
  height: 65rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1rpx solid #ddd;
  border-radius: 40rpx;
  font-size: 28rpx;
  color: #666;
  background-color: #fff;
}

.type-btn.active {
  background-color: #007aff;
  color: #fff;
  border-color: #007aff;
}

/* 已保存地址列表 */
.saved-address-section {
  padding: 0 40rpx 20rpx;
}

.saved-address-list {
  max-height: 400rpx;
}

/* 地址卡片样式 */
.address-card {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 30rpx 20rpx;
  margin-bottom: 20rpx;
  border-radius: 12rpx;
  background-color: #ffffff;
  border: 2rpx solid #f0f0f0;
  transition: all 0.3s ease;
  position: relative;
}

.address-card:last-child {
  margin-bottom: 0;
}

.address-card.selected {
  border-color: #007aff;
  background-color: rgba(0, 122, 255, 0.05);
  box-shadow: 0 4rpx 12rpx rgba(0, 122, 255, 0.15);
}

.address-main-info {
  flex: 1;
  padding-right: 20rpx;
}

.address-title {
  font-size: 30rpx;
  color: #333;
  font-weight: 500;
  display: block;
  margin-bottom: 12rpx;
  line-height: 1.4;
}

.address-detail-text {
  font-size: 26rpx;
  color: #666;
  display: block;
  line-height: 1.3;
}

.address-tags {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 8rpx;
}

.default-badge {
  background-color: #ff4391;
  color: #fff;
  font-size: 22rpx;
  padding: 6rpx 12rpx;
  border-radius: 12rpx;
  font-weight: 500;
}

.selected-badge {
  background-color: #007aff;
  color: #fff;
  font-size: 24rpx;
  width: 40rpx;
  height: 40rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2rpx 8rpx rgba(0, 122, 255, 0.3);
}

.selected-icon {
  font-size: 20rpx;
  font-weight: bold;
}

/* 空状态样式 */
.empty-address-state {
  text-align: center;
  padding: 80rpx 40rpx;
  color: #999;
}

.empty-icon {
  font-size: 80rpx;
  margin-bottom: 20rpx;
  opacity: 0.6;
}

.empty-address-state .empty-text {
  font-size: 28rpx;
  color: #999;
  display: block;
  margin-bottom: 12rpx;
}

.empty-tip {
  font-size: 24rpx;
  color: #ccc;
  display: block;
}

/* 地图选择区域 */
.map-select-section {
  padding: 0 40rpx 20rpx;
}

.map-select-card {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 30rpx 20rpx;
  border-radius: 12rpx;
  background-color: #ffffff;
  border: 2rpx dashed #007aff;
  transition: all 0.3s ease;
}

.map-select-card:active {
  background-color: rgba(0, 122, 255, 0.05);
  transform: scale(0.98);
}

.map-select-content {
  display: flex;
  align-items: center;
  flex: 1;
}

.map-icon-wrapper {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  background-color: rgba(0, 122, 255, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20rpx;
}

.map-icon {
  font-size: 32rpx;
}

.map-text-wrapper {
  flex: 1;
}

.map-title {
  font-size: 30rpx;
  color: #007aff;
  font-weight: 500;
  display: block;
  margin-bottom: 6rpx;
}

.map-subtitle {
  font-size: 24rpx;
  color: #999;
  display: block;
}

.map-arrow {
  font-size: 32rpx;
  color: #007aff;
  font-weight: bold;
}

/* 表单区域 */
.form-section {
  padding: 20rpx 40rpx;
  flex: 1;
  overflow-y: auto;
}

.form-item {
  margin-bottom: 30rpx;
  position: relative;
}

.form-label {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 12rpx;
  display: block;
}

.form-input {
  width: 100%;
  height: 80rpx;
  border: 1rpx solid #ddd;
  border-radius: 8rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
  background-color: #fff;
  box-sizing: border-box;
}

.form-textarea {
  width: 100%;
  min-height: 120rpx;
  border: 1rpx solid #ddd;
  border-radius: 8rpx;
  padding: 20rpx;
  font-size: 28rpx;
  background-color: #fff;
  box-sizing: border-box;
}

.char-count {
  position: absolute;
  right: 20rpx;
  bottom: 8rpx;
  font-size: 24rpx;
  color: #999;
}

/* 经纬度信息显示 */
.coordinate-info {
  margin-top: 20rpx;
  padding: 20rpx;
  background-color: #f8f9fa;
  border-radius: 8rpx;
  border-left: 4rpx solid #007aff;
}

.coordinate-label {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 8rpx;
}

.coordinate-text {
  font-size: 24rpx;
  color: #999;
  font-family: monospace;
}

/* 底部按钮 */
.modal-footer {
  display: flex;
  gap: 20rpx;
  padding: 30rpx 40rpx;
  border-top: 1rpx solid #f0f0f0;
}

.cancel-btn {
  flex: 1;
  border: 1rpx solid #ddd;
  border-radius: 40rpx;
  background-color: #fff;
  color: #666;
  font-size: 28rpx;
}

.confirm-btn {
  flex: 1;
  border: none;
  border-radius: 40rpx;
  background-color: #007aff;
  color: #fff;
  font-size: 28rpx;
}

.confirm-btn.loading {
  background-color: #ccc;
  color: #999;
}