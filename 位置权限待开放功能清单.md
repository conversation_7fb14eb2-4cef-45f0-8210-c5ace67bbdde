# 位置权限待开放功能清单

## 概述
由于微信小程序的位置权限（getLocation、chooseLocation）尚未申请下来，以下功能已被临时注释，等权限申请完成后需要恢复。

## 已注释的功能

### 1. 首页位置获取功能
**文件**: `pages/index/index.js`
**功能**: 获取用户当前位置，显示城市和区域信息，查找附近车辆
**注释位置**: 
- `getLocation()` 方法 (第192-224行)
- `_getActualLocation()` 方法 (第226-264行)

**临时方案**: 使用固定坐标（北京市朝阳区）进行测试

### 2. 地图页面位置获取功能
**文件**: `pages/map/map.js`
**功能**: 获取用户位置，在地图上显示用户位置和附近车辆
**注释位置**: 
- `updateLocation()` 方法 (第43-87行)

**临时方案**: 使用固定坐标（北京市中心）进行测试

### 3. 地址管理页面地图选择功能
**文件**: `pages/service/adress/addAdress.js`
**功能**: 通过地图选择位置，自动填充地址信息
**注释位置**: 
- `getLocation()` 方法 (第90-127行)
- `getActualLocation()` 方法 (第128-178行)

**临时方案**: 提示用户手动输入详细地址信息

### 4. 服务预约页面地图选择功能
**文件**: `pages/serviceOrder/index.js`
**功能**: 在服务预约时通过地图选择服务地址
**注释位置**: 
- `chooseMapLocation()` 方法 (第651-691行)

**临时方案**: 提示用户使用地址管理功能添加地址

### 5. 应用配置权限设置
**文件**: `app.json`
**功能**: 小程序权限配置
**注释位置**: 
- `requiredPrivateInfos` 数组已清空 (第53行)
- `requiredBackgroundModes` 数组已清空 (第52行)

**说明**: 位置权限相关配置已临时移除

## 权限申请完成后的恢复步骤

### 1. 恢复app.json配置
```json
"requiredBackgroundModes": ["location"],
"requiredPrivateInfos": ["getLocation", "chooseLocation"],
```

### 2. 恢复各页面功能
搜索所有包含 `TODO: 等getLocation权限申请下来后放开此功能` 或 `TODO: 等chooseLocation权限申请下来后放开此功能` 的注释，取消注释并删除临时代码。

### 3. 测试功能
- 首页位置显示和车辆查找
- 地图页面用户定位和车辆标记
- 地址管理的地图选择功能
- 服务预约的地址选择功能

## 注意事项

1. **权限申请**: 需要在微信公众平台申请 `getLocation` 和 `chooseLocation` 权限
2. **用户授权**: 恢复功能后需要处理用户拒绝授权的情况
3. **兼容性**: 确保在不同设备和微信版本上的兼容性
4. **错误处理**: 完善位置获取失败时的错误处理逻辑

## 相关API说明

- `wx.getLocation()`: 获取当前的地理位置、速度
- `wx.chooseLocation()`: 打开地图选择位置
- `wx.getSetting()`: 获取用户的当前设置
- `wx.authorize()`: 提前向用户发起授权请求

## 临时测试坐标

当前使用的固定测试坐标：
- 纬度: 39.9042 (北京市中心)
- 经度: 116.4074 (北京市中心)
- 城市: 北京市
- 区域: 朝阳区
